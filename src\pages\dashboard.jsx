// Archivo: src/pages/dashboard.jsx

import Head from 'next/head';

export default function Dashboard() {
  return (
    <div className="flex h-screen w-full bg-gradient-to-br from-blue-900 to-indigo-900 text-white">
      {/* Sidebar */}
      <aside className="w-60 bg-blue-950 p-4 flex flex-col">
        <div className="mb-6">
          <h1 className="text-xl font-bold">🚀 GERMAYORI</h1>
          <p className="text-sm">👤 Usuario GERMAYORI</p>
          <p className="text-yellow-400 text-xs">⭐ PREMIUM</p>
        </div>
        <nav className="space-y-2 text-sm">
          <a href="/chat" className="block hover:text-cyan-300">💬 Chat Educativo</a>
          <a href="/trading" className="block hover:text-cyan-300">📉 Trading en Vivo</a>
          <a href="/calculadora" className="block hover:text-cyan-300">🧮 Calculadora</a>
          <a href="/noticias" className="block hover:text-cyan-300">📰 Noticias</a>
          <a href="/senales" className="block hover:text-cyan-300">📡 Señales</a>
          <a href="/notificaciones" className="block hover:text-cyan-300">🔔 Notificaciones</a>
          <a href="/alertas" className="block hover:text-cyan-300">⚠️ Alertas Mercado</a>
          <a href="/tradingview" className="block hover:text-cyan-300">📉 TradingView</a>
          <p className="mt-4 text-xs text-gray-400">🎓 EDUCACIÓN GERMAYORI</p>
          <a href="/videos" className="block hover:text-cyan-300">📘 Videos Educativos <span className="bg-yellow-500 text-black rounded px-2 text-xs ml-1">PREMIUM</span></a>
          <a href="/mentoria" className="block hover:text-cyan-300">🎓 Mentoría Directa <span className="bg-pink-500 text-white rounded px-2 text-xs ml-1">VIP</span></a>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-8 overflow-auto">
        <Head>
          <title>Dashboard Germayori</title>
        </Head>
        <h2 className="text-3xl font-bold mb-4">Bienvenido a GERMAYORI</h2>
        <p className="mb-6 text-sm">Selecciona un canal para comenzar</p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card title="Chat Educativo" desc="Asistente IA con OpenAI" icon="💬" />
          <Card title="Trading en Vivo" desc="Operaciones reales jhon0608" icon="📉" tag="LIVE" />
          <Card title="Calculadora" desc="Pips, profit y riesgo" icon="🧮" />
          <Card title="Noticias" desc="Calendario económico" icon="📰" />
          <Card title="Señales" desc="Trading signals" icon="📡" />
          <Card title="Notificaciones" desc="Sistema de alertas" icon="🔔" />
          <Card title="Alertas" desc="Mercado en tiempo real" icon="⚠️" />
          <Card title="Videos Educativos" desc="Cursos GERMAYORI FVG" icon="📘" tag="PREMIUM" />
          <Card title="Mentoría Directa" desc="Con el creador de GERMAYORI" icon="🎓" tag="VIP EXCLUSIVO" />
        </div>
      </main>
    </div>
  );
}

function Card({ title, desc, icon, tag }) {
  return (
    <div className="bg-blue-800 rounded-xl p-4 shadow hover:scale-105 transition-transform">
      <div className="flex items-center justify-between mb-2">
        <span className="text-2xl">{icon}</span>
        {tag && <span className="bg-pink-500 text-white text-xs px-2 py-0.5 rounded-full">{tag}</span>}
      </div>
      <h3 className="font-bold text-lg">{title}</h3>
      <p className="text-sm text-gray-200">{desc}</p>
    </div>
  );
}
