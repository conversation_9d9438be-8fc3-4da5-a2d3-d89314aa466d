"use client";
import { useState } from "react";

export default function CanalSenales() {
  const [imagen, setImagen] = useState(null);
  const [resultado, setResultado] = useState("");
  const [cargando, setCargando] = useState(false);

  const enviarImagen = async (e) => {
    e.preventDefault();
    if (!imagen) return;

    const formData = new FormData();
    formData.append("file", imagen);
    setCargando(true);
    setResultado("⏳ Analizando imagen...");

    try {
      const res = await fetch("/api/analizar-imagen", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      setResultado(data.resultado || "❌ No se pudo generar la señal.");
    } catch (err) {
      setResultado("❌ Error al procesar la imagen.");
    } finally {
      setCargando(false);
    }
  };

  return (
    <main className="p-6 max-w-xl mx-auto text-center">
      <h1 className="text-2xl font-bold mb-4">📡 Canal de Señales Germayori</h1>
      <form onSubmit={enviarImagen} className="space-y-4">
        <input
          type="file"
          accept="image/*"
          onChange={(e) => setImagen(e.target.files[0])}
          className="block w-full"
          required
        />
        <button
          type="submit"
          className="bg-green-600 text-white px-4 py-2 rounded"
          disabled={cargando}
        >
          {cargando ? "Analizando..." : "📤 Enviar Imagen"}
        </button>
      </form>
      <div id="resultado" className="mt-6 whitespace-pre-wrap text-left text-base bg-gray-100 p-4 rounded">
        {resultado}
      </div>
    </main>
  );
}
