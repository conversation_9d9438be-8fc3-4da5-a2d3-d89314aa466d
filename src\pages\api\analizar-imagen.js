const formidable = require("formidable");
const fs = require("fs");
const OpenAI = require("openai");

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Método no permitido" });
  }

  const form = new formidable.IncomingForm({ multiples: false });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error("Error al parsear:", err);
      return res.status(500).json({ error: "Error al procesar la imagen." });
    }

    const file = files.file;
    if (!file) {
      return res.status(400).json({ error: "No se recibió ningún archivo." });
    }

    const filepath = Array.isArray(file) ? file[0].filepath : file.filepath;
    const imageBuffer = fs.readFileSync(filepath);
    const base64Image = imageBuffer.toString("base64");

    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 800,
        messages: [
          {
            role: "system",
            content:
              "Eres un experto en trading institucional. Analiza la imagen y responde con una señal clara como esta:\n\n🟢 COMPRA en XAUUSD\n📍 Entrada: 2,325.00\n⛔ SL: 2,310.00\n🎯 TP1: 2,340.00 | TP2: 2,355.00 | TP3: 2,370.00\n🧠 Justificación: Explica la razón de entrada sin mencionar soporte ni resistencia.",
          },
          {
            role: "user",
            content: [
              {
                type: "image_url",
                image_url: {
                  url: `data:image/png;base64,${base64Image}`,
                },
              },
            ],
          },
        ],
      });

      return res.status(200).json({ resultado: response.choices[0].message.content });
    } catch (error) {
      console.error("Error al llamar a OpenAI:", error);
      return res.status(500).json({ error: "Error al generar la señal." });
    }
  });
}
