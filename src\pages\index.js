import { useState } from 'react';
import Head from 'next/head';

export default function LandingPage() {
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    pais: ''
  });

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleRegistro = (e) => {
    e.preventDefault();
    // Aquí después conectaremos con MongoDB y pagos
    console.log('Registro:', formData);
    alert('¡Registro exitoso! Serás redirigido al pago...');
  };

  return (
    <>
      <Head>
        <title>GERMAYORI - Estrategia de Liquidez Institucional</title>
        <meta name="description" content="Aprende la estrategia de trading más avanzada con Germayori. Liquidez institucional, FVG y análisis profesional." />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-x-hidden">

        {/* Header */}
        <header className="relative z-10 p-6">
          <nav className="flex justify-between items-center max-w-7xl mx-auto">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center font-bold text-xl">
                G
              </div>
              <h1 className="text-2xl font-bold">GERMAYORI</h1>
            </div>
            <div className="hidden md:flex space-x-6">
              <a href="#estrategia" className="hover:text-orange-400 transition-colors">Estrategia</a>
              <a href="#resultados" className="hover:text-orange-400 transition-colors">Resultados</a>
              <a href="#registro" className="hover:text-orange-400 transition-colors">Registro</a>
            </div>
          </nav>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 px-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

              {/* Contenido Principal */}
              <div className="space-y-8">
                <div className="space-y-4">
                  <h2 className="text-5xl lg:text-7xl font-bold leading-tight">
                    <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
                      GERMAYORI
                    </span>
                  </h2>
                  <h3 className="text-2xl lg:text-3xl font-semibold text-gray-300">
                    Estrategia de Liquidez Institucional
                  </h3>
                  <p className="text-xl text-gray-400 leading-relaxed">
                    Descubre los secretos del trading institucional. Aprende a seguir la liquidez de los grandes bancos y opera como un profesional.
                  </p>
                </div>

                {/* Estadísticas Impresionantes */}
                <div className="grid grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400">85%</div>
                    <div className="text-sm text-gray-400">Win Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-400">2,500+</div>
                    <div className="text-sm text-gray-400">Pips Ganados</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-400">300%</div>
                    <div className="text-sm text-gray-400">ROI Promedio</div>
                  </div>
                </div>

                {/* Botón CTA */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href="#registro"
                    className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 text-center"
                  >
                    🚀 Acceder Ahora - $97/mes
                  </a>
                  <a
                    href="#estrategia"
                    className="border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all text-center"
                  >
                    📊 Ver Estrategia
                  </a>
                </div>
              </div>

              {/* Imagen de Germayori */}
              <div className="relative">
                <div className="relative z-10 bg-gradient-to-br from-blue-800 to-purple-900 rounded-2xl p-8 shadow-2xl">
                  <div className="text-center mb-6">
                    <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-red-500 rounded-full mx-auto flex items-center justify-center text-4xl font-bold mb-4">
                      G
                    </div>
                    <h4 className="text-2xl font-bold">Germayori</h4>
                    <p className="text-gray-300">Trader Institucional</p>
                  </div>

                  {/* Gráfica de Trading Simulada */}
                  <div className="bg-black rounded-lg p-4 mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-green-400">XAUUSD</span>
                      <span className="text-green-400">+2.45%</span>
                    </div>
                    <div className="h-24 flex items-end space-x-1">
                      {[...Array(20)].map((_, i) => (
                        <div
                          key={i}
                          className={`w-2 bg-gradient-to-t ${
                            Math.random() > 0.5 ? 'from-green-400 to-green-600' : 'from-red-400 to-red-600'
                          } rounded-t`}
                          style={{ height: `${Math.random() * 80 + 20}%` }}
                        ></div>
                      ))}
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-green-400 font-bold text-lg">+$1,250 HOY</div>
                    <div className="text-gray-400 text-sm">Ganancia en vivo</div>
                  </div>
                </div>

                {/* Efectos de fondo */}
                <div className="absolute -top-4 -right-4 w-72 h-72 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Sección de Estrategia */}
        <section id="estrategia" className="py-20 px-6 bg-black bg-opacity-20">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                  La Estrategia GERMAYORI
                </span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                No más Order Blocks tradicionales. Descubre cómo los bancos realmente mueven el mercado.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Liquidez Institucional */}
              <div className="bg-gradient-to-br from-blue-800 to-blue-900 rounded-xl p-8 text-center transform hover:scale-105 transition-all">
                <div className="text-5xl mb-4">🏦</div>
                <h3 className="text-2xl font-bold mb-4">Liquidez Institucional</h3>
                <p className="text-gray-300">
                  Identifica dónde los grandes bancos colocan sus órdenes masivas y síguelos para obtener ganancias consistentes.
                </p>
              </div>

              {/* Fair Value Gaps */}
              <div className="bg-gradient-to-br from-purple-800 to-purple-900 rounded-xl p-8 text-center transform hover:scale-105 transition-all">
                <div className="text-5xl mb-4">📊</div>
                <h3 className="text-2xl font-bold mb-4">Fair Value Gaps</h3>
                <p className="text-gray-300">
                  Aprovecha los desequilibrios del mercado que los algoritmos institucionales crean para maximizar tus entradas.
                </p>
              </div>

              {/* Análisis Multi-Temporal */}
              <div className="bg-gradient-to-br from-orange-800 to-red-900 rounded-xl p-8 text-center transform hover:scale-105 transition-all">
                <div className="text-5xl mb-4">⏰</div>
                <h3 className="text-2xl font-bold mb-4">Multi-Temporalidad</h3>
                <p className="text-gray-300">
                  Analiza 5 temporalidades simultáneamente para confirmar la dirección institucional del mercado.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Sección de Resultados */}
        <section id="resultados" className="py-20 px-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent">
                  Resultados Reales
                </span>
              </h2>
              <p className="text-xl text-gray-400">
                Más de 1,000 traders ya están generando ingresos consistentes
              </p>
            </div>

            {/* Gráfica de Rendimiento */}
            <div className="bg-gradient-to-br from-gray-900 to-black rounded-2xl p-8 mb-12">
              <h3 className="text-2xl font-bold mb-6 text-center">📈 Rendimiento Últimos 6 Meses</h3>
              <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
                {[
                  { mes: 'Jul', ganancia: 45, altura: '60%' },
                  { mes: 'Ago', ganancia: 67, altura: '80%' },
                  { mes: 'Sep', ganancia: 89, altura: '100%' },
                  { mes: 'Oct', ganancia: 123, altura: '90%' },
                  { mes: 'Nov', ganancia: 156, altura: '95%' },
                  { mes: 'Dic', ganancia: 189, altura: '85%' }
                ].map((data, index) => (
                  <div key={index} className="text-center">
                    <div className="h-32 flex items-end justify-center mb-2">
                      <div
                        className="w-8 bg-gradient-to-t from-green-400 to-green-600 rounded-t"
                        style={{ height: data.altura }}
                      ></div>
                    </div>
                    <div className="text-sm font-semibold text-green-400">+{data.ganancia}%</div>
                    <div className="text-xs text-gray-400">{data.mes}</div>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="bg-blue-900 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-400">1,247</div>
                  <div className="text-gray-400">Traders Activos</div>
                </div>
                <div className="bg-green-900 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-400">$2.4M</div>
                  <div className="text-gray-400">Ganancias Generadas</div>
                </div>
                <div className="bg-orange-900 rounded-lg p-4">
                  <div className="text-2xl font-bold text-orange-400">4.8/5</div>
                  <div className="text-gray-400">Calificación</div>
                </div>
              </div>
            </div>

            {/* Testimonios */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  nombre: "Carlos M.",
                  pais: "🇲🇽 México",
                  testimonio: "En 3 meses pasé de $500 a $2,100. La estrategia de liquidez es increíble.",
                  ganancia: "+320%"
                },
                {
                  nombre: "Ana R.",
                  pais: "🇨🇴 Colombia",
                  testimonio: "Nunca había visto algo tan preciso. Los FVG realmente funcionan.",
                  ganancia: "+280%"
                },
                {
                  nombre: "Luis P.",
                  pais: "🇪🇸 España",
                  testimonio: "Germayori cambió mi vida. Ahora trading es mi trabajo de tiempo completo.",
                  ganancia: "+450%"
                }
              ].map((testimonio, index) => (
                <div key={index} className="bg-gradient-to-br from-blue-800 to-purple-800 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center font-bold mr-4">
                      {testimonio.nombre.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold">{testimonio.nombre}</div>
                      <div className="text-sm text-gray-400">{testimonio.pais}</div>
                    </div>
                    <div className="ml-auto text-green-400 font-bold">{testimonio.ganancia}</div>
                  </div>
                  <p className="text-gray-300 italic">"{testimonio.testimonio}"</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Sección de Registro */}
        <section id="registro" className="py-20 px-6 bg-gradient-to-br from-orange-900 to-red-900">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                🚀 Únete a GERMAYORI
              </h2>
              <p className="text-xl text-orange-100">
                Acceso completo por solo $97/mes. Cancela cuando quieras.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-8 text-gray-900">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                {/* Formulario de Registro */}
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-gray-900">📝 Registro Rápido</h3>
                  <form onSubmit={handleRegistro} className="space-y-4">
                    <div>
                      <label className="block text-sm font-semibold mb-2">Nombre Completo</label>
                      <input
                        type="text"
                        name="nombre"
                        value={formData.nombre}
                        onChange={handleInputChange}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none"
                        placeholder="Tu nombre completo"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold mb-2">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold mb-2">Teléfono</label>
                      <input
                        type="tel"
                        name="telefono"
                        value={formData.telefono}
                        onChange={handleInputChange}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none"
                        placeholder="****** 567 8900"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold mb-2">País</label>
                      <select
                        name="pais"
                        value={formData.pais}
                        onChange={handleInputChange}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none"
                        required
                      >
                        <option value="">Selecciona tu país</option>
                        <option value="Mexico">🇲🇽 México</option>
                        <option value="Colombia">🇨🇴 Colombia</option>
                        <option value="España">🇪🇸 España</option>
                        <option value="Argentina">🇦🇷 Argentina</option>
                        <option value="Chile">🇨🇱 Chile</option>
                        <option value="Peru">🇵🇪 Perú</option>
                        <option value="Venezuela">🇻🇪 Venezuela</option>
                        <option value="Ecuador">🇪🇨 Ecuador</option>
                        <option value="USA">🇺🇸 Estados Unidos</option>
                        <option value="Otro">🌍 Otro</option>
                      </select>
                    </div>
                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-4 rounded-lg font-bold text-lg transition-all transform hover:scale-105"
                    >
                      💳 Registrarse y Pagar $97/mes
                    </button>
                  </form>
                </div>

                {/* Beneficios Incluidos */}
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-gray-900">✨ Todo Incluido</h3>
                  <div className="space-y-4">
                    {[
                      "🎯 Estrategia completa de liquidez institucional",
                      "📊 Análisis en tiempo real de 5 temporalidades",
                      "💬 Chat educativo con IA Germayori",
                      "📡 Señales de trading en vivo",
                      "📈 Acceso a TradingView profesional",
                      "🧮 Calculadora avanzada de riesgo",
                      "📱 Notificaciones push instantáneas",
                      "🎓 Videos educativos exclusivos",
                      "👨‍🏫 Mentoría directa con Germayori",
                      "💎 Acceso VIP al grupo de WhatsApp"
                    ].map((beneficio, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">{beneficio}</span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 p-4 bg-green-100 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-700">💰 Garantía 30 Días</div>
                      <div className="text-sm text-green-600">Si no estás satisfecho, te devolvemos tu dinero</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-12 px-6 bg-black">
          <div className="max-w-7xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center font-bold text-xl">
                G
              </div>
              <h3 className="text-2xl font-bold">GERMAYORI</h3>
            </div>
            <p className="text-gray-400 mb-4">
              La estrategia de trading más avanzada del mercado
            </p>
            <div className="flex justify-center space-x-6 text-sm text-gray-500">
              <span>© 2024 Germayori</span>
              <span>•</span>
              <span>Términos y Condiciones</span>
              <span>•</span>
              <span>Política de Privacidad</span>
            </div>
          </div>
        </footer>

      </div>
    </>
  );
}
