// Archivo: src/pages/dashboard.jsx

import Head from 'next/head';
import { useState } from 'react';

export default function Dashboard() {
  const [seccionActiva, setSeccionActiva] = useState('inicio');

  return (
    <div className="flex h-screen w-full bg-gradient-to-br from-blue-900 to-indigo-900 text-white">
      {/* Sidebar */}
      <aside className="w-60 bg-blue-950 p-4 flex flex-col">
        <div className="mb-6">
          <h1 className="text-xl font-bold">🚀 GERMAYORI</h1>
          <p className="text-sm">👤 Usuario GERMAYORI</p>
          <p className="text-yellow-400 text-xs">⭐ PREMIUM</p>
        </div>
        <nav className="space-y-2 text-sm">
          <button
            onClick={() => setSeccionActiva('inicio')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'inicio' ? 'text-cyan-300' : ''}`}
          >
            🏠 Inicio
          </button>
          <button
            onClick={() => setSeccionActiva('chat')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'chat' ? 'text-cyan-300' : ''}`}
          >
            💬 Chat Educativo
          </button>
          <button
            onClick={() => setSeccionActiva('trading')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'trading' ? 'text-cyan-300' : ''}`}
          >
            📉 Trading en Vivo
          </button>
          <button
            onClick={() => setSeccionActiva('calculadora')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'calculadora' ? 'text-cyan-300' : ''}`}
          >
            🧮 Calculadora
          </button>
          <button
            onClick={() => setSeccionActiva('noticias')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'noticias' ? 'text-cyan-300' : ''}`}
          >
            📰 Noticias
          </button>
          <button
            onClick={() => setSeccionActiva('senales')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'senales' ? 'text-cyan-300' : ''}`}
          >
            📡 Señales
          </button>
          <button
            onClick={() => setSeccionActiva('notificaciones')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'notificaciones' ? 'text-cyan-300' : ''}`}
          >
            🔔 Notificaciones
          </button>
          <button
            onClick={() => setSeccionActiva('alertas')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'alertas' ? 'text-cyan-300' : ''}`}
          >
            ⚠️ Alertas Mercado
          </button>
          <button
            onClick={() => setSeccionActiva('tradingview')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'tradingview' ? 'text-cyan-300' : ''}`}
          >
            📉 TradingView
          </button>
          <p className="mt-4 text-xs text-gray-400">🎓 EDUCACIÓN GERMAYORI</p>
          <button
            onClick={() => setSeccionActiva('videos')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'videos' ? 'text-cyan-300' : ''}`}
          >
            📘 Videos Educativos <span className="bg-yellow-500 text-black rounded px-2 text-xs ml-1">PREMIUM</span>
          </button>
          <button
            onClick={() => setSeccionActiva('mentoria')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'mentoria' ? 'text-cyan-300' : ''}`}
          >
            🎓 Mentoría Directa <span className="bg-pink-500 text-white rounded px-2 text-xs ml-1">VIP</span>
          </button>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-8 overflow-auto">
        <Head>
          <title>Dashboard Germayori</title>
        </Head>

        {seccionActiva === 'inicio' && <SeccionInicio setSeccionActiva={setSeccionActiva} />}
        {seccionActiva === 'senales' && <SeccionSenales />}
        {seccionActiva === 'chat' && <SeccionEnDesarrollo titulo="Chat Educativo" />}
        {seccionActiva === 'trading' && <SeccionEnDesarrollo titulo="Trading en Vivo" />}
        {seccionActiva === 'calculadora' && <SeccionEnDesarrollo titulo="Calculadora" />}
        {seccionActiva === 'noticias' && <SeccionEnDesarrollo titulo="Noticias" />}
        {seccionActiva === 'notificaciones' && <SeccionEnDesarrollo titulo="Notificaciones" />}
        {seccionActiva === 'alertas' && <SeccionEnDesarrollo titulo="Alertas Mercado" />}
        {seccionActiva === 'tradingview' && <SeccionEnDesarrollo titulo="TradingView" />}
        {seccionActiva === 'videos' && <SeccionEnDesarrollo titulo="Videos Educativos" />}
        {seccionActiva === 'mentoria' && <SeccionEnDesarrollo titulo="Mentoría Directa" />}
      </main>
    </div>
  );
}

// Componente de la sección de inicio
function SeccionInicio({ setSeccionActiva }) {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">Bienvenido a GERMAYORI</h2>
      <p className="mb-6 text-sm">Selecciona un canal para comenzar</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card title="Chat Educativo" desc="Asistente IA con OpenAI" icon="💬" onClick={() => setSeccionActiva('chat')} />
        <Card title="Trading en Vivo" desc="Operaciones reales jhon0608" icon="📉" tag="LIVE" onClick={() => setSeccionActiva('trading')} />
        <Card title="Calculadora" desc="Pips, profit y riesgo" icon="🧮" onClick={() => setSeccionActiva('calculadora')} />
        <Card title="Noticias" desc="Calendario económico" icon="📰" onClick={() => setSeccionActiva('noticias')} />
        <Card title="Señales" desc="Trading signals" icon="📡" onClick={() => setSeccionActiva('senales')} />
        <Card title="Notificaciones" desc="Sistema de alertas" icon="🔔" onClick={() => setSeccionActiva('notificaciones')} />
        <Card title="Alertas" desc="Mercado en tiempo real" icon="⚠️" onClick={() => setSeccionActiva('alertas')} />
        <Card title="Videos Educativos" desc="Cursos GERMAYORI FVG" icon="📘" tag="PREMIUM" onClick={() => setSeccionActiva('videos')} />
        <Card title="Mentoría Directa" desc="Con el creador de GERMAYORI" icon="🎓" tag="VIP EXCLUSIVO" onClick={() => setSeccionActiva('mentoria')} />
      </div>
    </>
  );
}

// Componente de la sección de señales integrada
function SeccionSenales() {
  const [imagen, setImagen] = useState(null);
  const [resultado, setResultado] = useState("");
  const [cargando, setCargando] = useState(false);

  const enviarImagen = async (e) => {
    e.preventDefault();
    if (!imagen) return;

    const formData = new FormData();
    formData.append("file", imagen);
    setCargando(true);
    setResultado("⏳ Analizando imagen...");

    try {
      const res = await fetch("/api/analizar-imagen", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      setResultado(data.resultado || "❌ No se pudo generar la señal.");
    } catch (err) {
      setResultado("❌ Error al procesar la imagen.");
    } finally {
      setCargando(false);
    }
  };

  return (
    <>
      <div className="flex items-center mb-6">
        <h2 className="text-3xl font-bold">📡 Canal de Señales Germayori</h2>
      </div>

      <div className="bg-blue-800 rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-semibold mb-4">📤 Subir Imagen para Análisis</h3>

        <form onSubmit={enviarImagen} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Seleccionar imagen:</label>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => setImagen(e.target.files[0])}
              className="bg-white text-black p-3 rounded-lg w-full max-w-md"
              required
            />
          </div>

          <button
            type="submit"
            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors disabled:opacity-50"
            disabled={cargando}
          >
            📤 {cargando ? "Analizando..." : "Enviar Imagen"}
          </button>
        </form>

        <div className="mt-6">
          <h4 className="text-lg font-semibold mb-3">📊 Resultado del Análisis:</h4>
          <div
            className="bg-blue-900 rounded-lg p-4 text-lg whitespace-pre-wrap border-l-4 border-cyan-400"
            style={{ minHeight: "120px" }}
          >
            {resultado || "🔍 Sube una imagen para obtener el análisis de señales..."}
          </div>
        </div>
      </div>
    </>
  );
}

// Componente para secciones en desarrollo
function SeccionEnDesarrollo({ titulo }) {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">{titulo}</h2>
      <div className="bg-blue-800 rounded-xl p-8 text-center">
        <div className="text-6xl mb-4">🚧</div>
        <h3 className="text-xl font-semibold mb-2">Sección en Desarrollo</h3>
        <p className="text-gray-300">Esta funcionalidad estará disponible próximamente.</p>
      </div>
    </>
  );
}

function Card({ title, desc, icon, tag, onClick }) {
  return (
    <div
      className="bg-blue-800 rounded-xl p-4 shadow hover:scale-105 transition-transform cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-2">
        <span className="text-2xl">{icon}</span>
        {tag && <span className="bg-pink-500 text-white text-xs px-2 py-0.5 rounded-full">{tag}</span>}
      </div>
      <h3 className="font-bold text-lg">{title}</h3>
      <p className="text-sm text-gray-200">{desc}</p>
    </div>
  );
}
